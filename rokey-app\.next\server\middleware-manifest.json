{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "23ZdWmpTgMv8UNhfItg2w", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "69a6a26578c931270155816ee5286a88", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2d739fdf18255631cd952684b841aa8d0fcd89ad0fe3138823bfa63b1cfb7e8c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4f0e863e7ff93440d3f26fe287f4d25c5ce5c9b7c0ab0026dac850d0b17700dc"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "23ZdWmpTgMv8UNhfItg2w", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "69a6a26578c931270155816ee5286a88", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2d739fdf18255631cd952684b841aa8d0fcd89ad0fe3138823bfa63b1cfb7e8c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4f0e863e7ff93440d3f26fe287f4d25c5ce5c9b7c0ab0026dac850d0b17700dc"}}}, "sortedMiddleware": ["/"]}