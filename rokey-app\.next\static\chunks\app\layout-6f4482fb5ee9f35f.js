(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6703,9558],{1571:(e,t,r)=>{"use strict";r.d(t,{$:()=>c.A,f:()=>n.A});var c=r(78046),n=r(74500)},8652:(e,t,r)=>{"use strict";r.d(t,{AQ:()=>a.A,RY:()=>u.A,cu:()=>n.A,fA:()=>s.A,r9:()=>i.A,sR:()=>l.A,tl:()=>c.A});var c=r(94648),n=r(14615),i=r(5500),a=r(92975),s=r(69994),u=r(94038),l=r(39883)},10222:()=>{},10501:()=>{},18685:(e,t,r)=>{"use strict";r.d(t,{AQ:()=>a.A,RY:()=>u.A,cu:()=>n.A,fA:()=>s.A,r9:()=>i.A,sR:()=>l.A,tl:()=>c.A});var c=r(51297),n=r(48612),i=r(10187),a=r(55616),s=r(34853),u=r(11595),l=r(22670)},21884:(e,t,r)=>{"use strict";r.d(t,{C1:()=>c.A,KS:()=>i.A,Pi:()=>n.A,fK:()=>s.A,qh:()=>a.A});var c=r(6865),n=r(55628),i=r(67695),a=r(52589),s=r(74500)},22261:(e,t,r)=>{"use strict";r.d(t,{G:()=>a,c:()=>s});var c=r(95155),n=r(12115);let i=(0,n.createContext)(void 0);function a(e){let{children:t}=e,[r,a]=(0,n.useState)(!0),[s,u]=(0,n.useState)(!1),[l,o]=(0,n.useState)(!1);return(0,c.jsx)(i.Provider,{value:{isCollapsed:r,isHovered:s,isHoverDisabled:l,toggleSidebar:()=>a(!r),collapseSidebar:()=>a(!0),expandSidebar:()=>a(!1),setHovered:e=>{l||u(e)},setHoverDisabled:e=>{o(e),e&&u(!1)}},children:t})}function s(){let e=(0,n.useContext)(i);if(void 0===e)throw Error("useSidebar must be used within a SidebarProvider");return e}},27016:(e,t,r)=>{"use strict";r.d(t,{$p:()=>o.A,AQ:()=>l.A,BF:()=>u.A,D3:()=>a.A,Rz:()=>c.A,Vy:()=>s.A,XF:()=>i.A,tK:()=>n.A});var c=r(55596),n=r(69598),i=r(58828),a=r(63418),s=r(37186),u=r(40710),l=r(92975),o=r(78046)},30347:()=>{},31430:(e,t,r)=>{"use strict";r.d(t,{AQ:()=>a.A,DP:()=>i.A,RY:()=>s.A,cu:()=>c.A,r9:()=>n.A,sR:()=>u.A});var c=r(14615),n=r(5500),i=r(5246),a=r(92975),s=r(94038),u=r(39883)},66435:()=>{},86631:(e,t,r)=>{Promise.resolve().then(r.bind(r,48031)),Promise.resolve().then(r.t.bind(r,69243,23)),Promise.resolve().then(r.t.bind(r,80527,23)),Promise.resolve().then(r.t.bind(r,30347,23)),Promise.resolve().then(r.bind(r,35462)),Promise.resolve().then(r.bind(r,99030)),Promise.resolve().then(r.bind(r,52469)),Promise.resolve().then(r.bind(r,38050)),Promise.resolve().then(r.t.bind(r,10222,23)),Promise.resolve().then(r.t.bind(r,66435,23)),Promise.resolve().then(r.t.bind(r,10501,23))},86973:(e,t,r)=>{"use strict";r.d(t,{globalCache:()=>n});class c{set(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{ttl:c=3e5,tags:n=[],priority:i="medium",serialize:a=!1}=r,s={data:a?JSON.parse(JSON.stringify(t)):t,timestamp:Date.now(),ttl:c,accessCount:0,lastAccessed:Date.now(),tags:n,priority:i};this.cache.size>=this.maxSize&&this.evictLeastUsed(),this.cache.set(e,s)}get(e){let t=this.cache.get(e);return t?this.isExpired(t)?(this.cache.delete(e),null):(t.accessCount++,t.lastAccessed=Date.now(),t.data):null}getStale(e){let t=this.cache.get(e);if(!t)return{data:null,isStale:!1};let r=this.isExpired(t);return t.accessCount++,t.lastAccessed=Date.now(),{data:t.data,isStale:r}}has(e){let t=this.cache.get(e);return!!t&&(!this.isExpired(t)||(this.cache.delete(e),!1))}delete(e){return this.cache.delete(e)}invalidateByTags(e){let t=0;for(let[r,c]of this.cache.entries())c.tags.some(t=>e.includes(t))&&(this.cache.delete(r),t++);return t}clear(){this.cache.clear()}getStats(){let e=Array.from(this.cache.values()),t=Date.now();return{size:this.cache.size,maxSize:this.maxSize,hitRate:this.calculateHitRate(),averageAge:e.reduce((e,r)=>e+(t-r.timestamp),0)/e.length||0,totalAccesses:e.reduce((e,t)=>e+t.accessCount,0),expiredEntries:e.filter(e=>this.isExpired(e)).length,priorityDistribution:{high:e.filter(e=>"high"===e.priority).length,medium:e.filter(e=>"medium"===e.priority).length,low:e.filter(e=>"low"===e.priority).length}}}async preload(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},c=this.get(e);if(c)return this.backgroundRefresh(e,t,r),c;let n=await t();return this.set(e,n,r),n}async backgroundRefresh(e,t,r){try{let c=await t();this.set(e,c,r)}catch(e){}}isExpired(e){return Date.now()-e.timestamp>e.ttl}evictLeastUsed(){if(0===this.cache.size)return;let[e]=Array.from(this.cache.entries()).sort((e,t)=>{let[,r]=e,[,c]=t,n={low:0,medium:1,high:2},i=n[r.priority]-n[c.priority];return 0!==i?i:r.lastAccessed-c.lastAccessed})[0];this.cache.delete(e)}calculateHitRate(){let e=Array.from(this.cache.values()).reduce((e,t)=>e+t.accessCount,0);return e>0?this.cache.size/e*100:0}startCleanup(){this.cleanupInterval=setInterval(()=>{this.cleanup()},6e4)}cleanup(){Date.now();let e=[];for(let[t,r]of this.cache.entries())this.isExpired(r)&&e.push(t);e.forEach(e=>this.cache.delete(e)),e.length}destroy(){this.cleanupInterval&&clearInterval(this.cleanupInterval),this.cache.clear()}constructor(e=100){this.cache=new Map,this.cleanupInterval=null,this.maxSize=e,this.startCleanup()}}let n=new c(200)},89732:(e,t,r)=>{"use strict";r.d(t,{AQ:()=>a.A,RY:()=>u.A,cu:()=>n.A,fA:()=>s.A,fK:()=>o.A,r9:()=>i.A,sR:()=>l.A,tl:()=>c.A});var c=r(94648),n=r(14615),i=r(5500),a=r(92975),s=r(69994),u=r(94038),l=r(39883),o=r(74500)},99323:(e,t,r)=>{"use strict";r.d(t,{bu:()=>u,i9:()=>s});var c=r(95155),n=r(12115),i=r(35695);let a=(0,n.createContext)(void 0);function s(e){let{children:t}=e,[r,s]=(0,n.useState)(!1),[u,l]=(0,n.useState)(null),[o,h]=(0,n.useState)([]),[d,A]=(0,n.useState)(new Set),[f,v]=(0,n.useState)(!1),m=(0,i.usePathname)(),g=(0,i.useRouter)(),I=(0,n.useRef)(null),D=(0,n.useRef)([]),p=(0,n.useRef)(null),T=(0,n.useRef)(0),S=(0,n.useRef)({}),b=(0,n.useRef)({});(0,n.useEffect)(()=>{v(!0)},[]);let C=(0,n.useCallback)(e=>{},[f]);(0,n.useEffect)(()=>{m&&!o.includes(m)&&(h(e=>[...e,m]),A(e=>new Set([...e,m])))},[m,o]),(0,n.useEffect)(()=>{C("\uD83D\uDD0D [OPTIMISTIC NAV] Route check: target=".concat(u,", current=").concat(m,", navigationId=").concat(p.current)),u&&p.current&&m===u&&(C("✅ [OPTIMISTIC NAV] Navigation completed: ".concat(u," -> ").concat(m)),I.current&&(clearTimeout(I.current),I.current=null),s(!1),l(null),p.current=null,D.current=D.current.filter(e=>e.route!==u))},[m,u,C]),(0,n.useEffect)(()=>{r&&u&&m===u&&(C("\uD83D\uDE80 [OPTIMISTIC NAV] Immediate route match detected, clearing navigation state"),s(!1),l(null),I.current&&(clearTimeout(I.current),I.current=null))},[m,u,r,C]);let P=(0,n.useCallback)(e=>d.has(e),[d]),w=(0,n.useCallback)(()=>{if(0===D.current.length)return;let e=D.current[D.current.length-1];D.current=[e];let{route:t,id:r}=e;C("\uD83D\uDE80 [OPTIMISTIC NAV] Processing navigation to: ".concat(t," (id: ").concat(r,")")),I.current&&(clearTimeout(I.current),I.current=null),p.current=r;let c=P(t);c&&(C("⚡ [OPTIMISTIC NAV] Using cached navigation for: ".concat(t)),setTimeout(()=>{p.current===r&&s(!1)},100));try{g.push(t)}catch(e){C("❌ [OPTIMISTIC NAV] Router.push failed for: ".concat(t,", using fallback")),window.location.href=t;return}I.current=setTimeout(()=>{if(C("⚠️ [OPTIMISTIC NAV] Timeout reached for: ".concat(t," (id: ").concat(r,"), current path: ").concat(m)),p.current===r){C("\uD83D\uDD04 [OPTIMISTIC NAV] Attempting fallback navigation to: ".concat(t));try{window.location.href=t}catch(e){C("❌ [OPTIMISTIC NAV] Fallback navigation failed: ".concat(e))}s(!1),l(null),p.current=null}I.current=null},c?800:3e3)},[g,m,P,C]),E=(0,n.useCallback)(e=>{if(m===e||!f)return;let t=Date.now();if(t-T.current<100&&u===e)return void C("\uD83D\uDD04 [OPTIMISTIC NAV] Debouncing duplicate navigation to: ".concat(e));if(T.current=t,S.current[e]||(S.current[e]=0),S.current[e]++,b.current[e]&&clearTimeout(b.current[e]),b.current[e]=setTimeout(()=>{S.current[e]=0},2e3),S.current[e]>=3){C("\uD83D\uDEA8 [OPTIMISTIC NAV] Force navigation escape hatch for: ".concat(e)),S.current[e]=0,window.location.href=e;return}I.current&&(clearTimeout(I.current),I.current=null),s(!0),l(e);let r="nav_".concat(t,"_").concat(Math.random().toString(36).substr(2,9));D.current=[{route:e,timestamp:t,id:r}],w()},[m,u,w,C,f]),R=(0,n.useCallback)(()=>{I.current&&(clearTimeout(I.current),I.current=null),s(!1),l(null),p.current=null,D.current=[]},[]);return(0,n.useEffect)(()=>{if(!f)return;let e=()=>{!document.hidden&&r&&(C("\uD83D\uDC41️ [OPTIMISTIC NAV] Document visible, checking if navigation should clear"),setTimeout(()=>{u&&m===u&&(C("\uD83D\uDD27 [OPTIMISTIC NAV] Force clearing navigation state"),s(!1),l(null),I.current&&(clearTimeout(I.current),I.current=null))},100))};return document.addEventListener("visibilitychange",e),()=>document.removeEventListener("visibilitychange",e)},[r,u,m,C,f]),(0,n.useEffect)(()=>()=>{I.current&&clearTimeout(I.current)},[]),(0,c.jsx)(a.Provider,{value:{isNavigating:r,targetRoute:u,navigateOptimistically:E,clearNavigation:R,isPageCached:P,navigationHistory:o},children:t})}function u(){return(0,n.useContext)(a)||null}}},e=>{var t=t=>e(e.s=t);e.O(0,[2098,7690,9123,8888,1459,5738,9968,6060,6308,4755,563,2662,8669,4703,622,2432,408,9491,6642,7706,7544,2138,4518,9248,2324,7358],()=>t(86631)),_N_E=e.O()}]);