(()=>{"use strict";var e={},t={};function r(a){var c=t[a];if(void 0!==c)return c.exports;var n=t[a]={exports:{}},o=!0;try{e[a].call(n.exports,n,n.exports,r),o=!1}finally{o&&delete t[a]}return n.exports}r.m=e,(()=>{var e=[];r.O=(t,a,c,n)=>{if(a){n=n||0;for(var o=e.length;o>0&&e[o-1][2]>n;o--)e[o]=e[o-1];e[o]=[a,c,n];return}for(var d=1/0,o=0;o<e.length;o++){for(var[a,c,n]=e[o],s=!0,f=0;f<a.length;f++)(!1&n||d>=n)&&Object.keys(r.O).every(e=>r.O[e](a[f]))?a.splice(f--,1):(s=!1,n<d&&(d=n));if(s){e.splice(o--,1);var i=c();void 0!==i&&(t=i)}}return t}})(),r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;r.t=function(a,c){if(1&c&&(a=this(a)),8&c||"object"==typeof a&&a&&(4&c&&a.__esModule||16&c&&"function"==typeof a.then))return a;var n=Object.create(null);r.r(n);var o={};e=e||[null,t({}),t([]),t(t)];for(var d=2&c&&a;"object"==typeof d&&!~e.indexOf(d);d=t(d))Object.getOwnPropertyNames(d).forEach(e=>o[e]=()=>a[e]);return o.default=()=>a,r.d(n,o),n}})(),r.d=(e,t)=>{for(var a in t)r.o(t,a)&&!r.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},r.f={},r.e=e=>Promise.all(Object.keys(r.f).reduce((t,a)=>(r.f[a](e,t),t),[])),r.u=e=>6308===e?"static/chunks/vendors-7237a82e-5b1f53a4b8780d18.js":4755===e?"static/chunks/vendors-0925edb1-7c86a3ef9dc28918.js":563===e?"static/chunks/vendors-ad6a2f20-003cc44fa9df4a8c.js":2662===e?"static/chunks/vendors-04fef8b0-2eb254c773caab4c.js":8669===e?"static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js":4703===e?"static/chunks/vendors-8b9b2362-31ce7ba97daac261.js":622===e?"static/chunks/vendors-b49fab05-d0aff3e13bac17b6.js":2432===e?"static/chunks/vendors-27f02048-d5ca4f1bc0e362d3.js":408===e?"static/chunks/vendors-fa70753b-4771c7ba0a87fef8.js":5738===e?"static/chunks/utils-82f2e22facbee329.js":8888===e?"static/chunks/supabase-55776fae-704411a5287a30d1.js":1459===e?"static/chunks/supabase-0d08456b-7b71d5b8f6389e71.js":9968===e?"static/chunks/ui-components-b80f15b3-d706430e6d29d4e9.js":6060===e?"static/chunks/ui-components-3acb5f41-677031179f92dbfa.js":"static/chunks/"+(({703:"markdown-cd8c40e0",2548:"markdown-5582deac",3285:"markdown-f75080aa",3466:"playground-heavy",4280:"markdown-b1f8c777",4726:"markdown-f393dd55",5006:"markdown-dbb68ab2",5928:"markdown-c3128679",6703:"performance",8960:"markdown-b2d55df5",8961:"markdown-98dda3e8"})[e]||e)+"."+({678:"e5d58f9d442dd47e",703:"cef8ce15008477e7",2548:"88731d1f9c3245f8",3285:"fcb3b50b332effcf",3310:"fe0de0d8efce7dfe",3466:"5b69ab59c5734c20",3613:"e2ec2ddc8da25689",4280:"dd13d2cb885d039c",4726:"0a7ac94c5b549034",5006:"4e0bc25d935d6fa3",5260:"4a3cc312b7749e5a",5721:"26191678a4be675f",5928:"755da21a8b30fb82",6703:"c9c20ad2ce72a0cb",7096:"7f1bb11b6d9491bd",7455:"64aa8767398d45d3",7525:"53d09120a34ffe5d",8730:"b1e2fe83d2bd8280",8960:"90162d707f62ebb4",8961:"bad213a2f828f9fe"})[e]+".js",r.miniCssF=e=>{},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="_N_E:";r.l=(a,c,n,o)=>{if(e[a])return void e[a].push(c);if(void 0!==n)for(var d,s,f=document.getElementsByTagName("script"),i=0;i<f.length;i++){var u=f[i];if(u.getAttribute("src")==a||u.getAttribute("data-webpack")==t+n){d=u;break}}d||(s=!0,(d=document.createElement("script")).charset="utf-8",d.timeout=120,r.nc&&d.setAttribute("nonce",r.nc),d.setAttribute("data-webpack",t+n),d.src=r.tu(a)),e[a]=[c];var b=(t,r)=>{d.onerror=d.onload=null,clearTimeout(l);var c=e[a];if(delete e[a],d.parentNode&&d.parentNode.removeChild(d),c&&c.forEach(e=>e(r)),t)return t(r)},l=setTimeout(b.bind(null,void 0,{type:"timeout",target:d}),12e4);d.onerror=b.bind(null,d.onerror),d.onload=b.bind(null,d.onload),s&&document.head.appendChild(d)}})(),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;r.tt=()=>(void 0===e&&(e={createScriptURL:e=>e},"undefined"!=typeof trustedTypes&&trustedTypes.createPolicy&&(e=trustedTypes.createPolicy("nextjs#bundler",e))),e)})(),r.tu=e=>r.tt().createScriptURL(e),r.p="/_next/",(()=>{var e={8068:0,2098:0,7690:0,9123:0};r.f.j=(t,a)=>{var c=r.o(e,t)?e[t]:void 0;if(0!==c)if(c)a.push(c[2]);else if(/^(2098|7690|8068|9123)$/.test(t))e[t]=0;else{var n=new Promise((r,a)=>c=e[t]=[r,a]);a.push(c[2]=n);var o=r.p+r.u(t),d=Error();r.l(o,a=>{if(r.o(e,t)&&(0!==(c=e[t])&&(e[t]=void 0),c)){var n=a&&("load"===a.type?"missing":a.type),o=a&&a.target&&a.target.src;d.message="Loading chunk "+t+" failed.\n("+n+": "+o+")",d.name="ChunkLoadError",d.type=n,d.request=o,c[1](d)}},"chunk-"+t,t)}},r.O.j=t=>0===e[t];var t=(t,a)=>{var c,n,[o,d,s]=a,f=0;if(o.some(t=>0!==e[t])){for(c in d)r.o(d,c)&&(r.m[c]=d[c]);if(s)var i=s(r)}for(t&&t(a);f<o.length;f++)n=o[f],r.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return r.O(i)},a=self.webpackChunk_N_E=self.webpackChunk_N_E||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))})(),r.nc=void 0})();