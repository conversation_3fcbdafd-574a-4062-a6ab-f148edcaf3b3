"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[682],{1243:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},22475:(e,t,n)=>{n.d(t,{UE:()=>ea,ll:()=>er,rD:()=>eu,UU:()=>el,cY:()=>eo,BN:()=>ei});let r=Math.min,o=Math.max,i=Math.round,l=Math.floor,a=e=>({x:e,y:e}),u={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function s(e,t){return"function"==typeof e?e(t):e}function d(e){return e.split("-")[0]}function f(e){return e.split("-")[1]}function p(e){return"x"===e?"y":"x"}function m(e){return"y"===e?"height":"width"}function v(e){return["top","bottom"].includes(d(e))?"y":"x"}function h(e){return e.replace(/start|end/g,e=>c[e])}function g(e){return e.replace(/left|right|bottom|top/g,e=>u[e])}function y(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function w(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function b(e,t,n){let r,{reference:o,floating:i}=e,l=v(t),a=p(v(t)),u=m(a),c=d(t),s="y"===l,h=o.x+o.width/2-i.width/2,g=o.y+o.height/2-i.height/2,y=o[u]/2-i[u]/2;switch(c){case"top":r={x:h,y:o.y-i.height};break;case"bottom":r={x:h,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:g};break;case"left":r={x:o.x-i.width,y:g};break;default:r={x:o.x,y:o.y}}switch(f(t)){case"start":r[a]-=y*(n&&s?-1:1);break;case"end":r[a]+=y*(n&&s?-1:1)}return r}let E=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=b(c,r,u),f=r,p={},m=0;for(let n=0;n<a.length;n++){let{name:i,fn:v}=a[n],{x:h,y:g,data:y,reset:w}=await v({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=h?h:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=b(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function x(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:f="floating",altBoundary:p=!1,padding:m=0}=s(t,e),v=y(m),h=a[p?"floating"===f?"reference":"floating":f],g=w(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(h)))||n?h:h.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:d,strategy:u})),b="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,E=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),x=await (null==i.isElement?void 0:i.isElement(E))&&await (null==i.getScale?void 0:i.getScale(E))||{x:1,y:1},R=w(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:b,offsetParent:E,strategy:u}):b);return{top:(g.top-R.top+v.top)/x.y,bottom:(R.bottom-g.bottom+v.bottom)/x.y,left:(g.left-R.left+v.left)/x.x,right:(R.right-g.right+v.right)/x.x}}async function R(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=d(n),a=f(n),u="y"===v(n),c=["left","top"].includes(l)?-1:1,p=i&&u?-1:1,m=s(t,e),{mainAxis:h,crossAxis:g,alignmentAxis:y}="number"==typeof m?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return a&&"number"==typeof y&&(g="end"===a?-1*y:y),u?{x:g*p,y:h*c}:{x:h*c,y:g*p}}function C(){return"undefined"!=typeof window}function k(e){return N(e)?(e.nodeName||"").toLowerCase():"#document"}function L(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function T(e){var t;return null==(t=(N(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function N(e){return!!C()&&(e instanceof Node||e instanceof L(e).Node)}function A(e){return!!C()&&(e instanceof Element||e instanceof L(e).Element)}function O(e){return!!C()&&(e instanceof HTMLElement||e instanceof L(e).HTMLElement)}function M(e){return!!C()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof L(e).ShadowRoot)}function S(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=F(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function D(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function P(e){let t=j(),n=A(e)?F(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function j(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function I(e){return["html","body","#document"].includes(k(e))}function F(e){return L(e).getComputedStyle(e)}function W(e){return A(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function _(e){if("html"===k(e))return e;let t=e.assignedSlot||e.parentNode||M(e)&&e.host||T(e);return M(t)?t.host:t}function B(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=_(t);return I(n)?t.ownerDocument?t.ownerDocument.body:t.body:O(n)&&S(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=L(o);if(i){let e=H(l);return t.concat(l,l.visualViewport||[],S(o)?o:[],e&&n?B(e):[])}return t.concat(o,B(o,[],n))}function H(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function U(e){let t=F(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=O(e),l=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=i(n)!==l||i(r)!==a;return u&&(n=l,r=a),{width:n,height:r,$:u}}function z(e){return A(e)?e:e.contextElement}function V(e){let t=z(e);if(!O(t))return a(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:l}=U(t),u=(l?i(n.width):n.width)/r,c=(l?i(n.height):n.height)/o;return u&&Number.isFinite(u)||(u=1),c&&Number.isFinite(c)||(c=1),{x:u,y:c}}let $=a(0);function Y(e){let t=L(e);return j()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:$}function G(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=z(e),u=a(1);t&&(r?A(r)&&(u=V(r)):u=V(e));let c=(void 0===(o=n)&&(o=!1),r&&(!o||r===L(l))&&o)?Y(l):a(0),s=(i.left+c.x)/u.x,d=(i.top+c.y)/u.y,f=i.width/u.x,p=i.height/u.y;if(l){let e=L(l),t=r&&A(r)?L(r):r,n=e,o=H(n);for(;o&&r&&t!==n;){let e=V(o),t=o.getBoundingClientRect(),r=F(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,d*=e.y,f*=e.x,p*=e.y,s+=i,d+=l,o=H(n=L(o))}}return w({width:f,height:p,x:s,y:d})}function K(e,t){let n=W(e).scrollLeft;return t?t.left+n:G(T(e)).left+n}function q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:K(e,r)),y:r.top+t.scrollTop}}function X(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=L(e),r=T(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=j();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=T(e),n=W(e),r=e.ownerDocument.body,i=o(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=o(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+K(e),u=-n.scrollTop;return"rtl"===F(r).direction&&(a+=o(t.clientWidth,r.clientWidth)-i),{width:i,height:l,x:a,y:u}}(T(e));else if(A(t))r=function(e,t){let n=G(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=O(e)?V(e):a(1),l=e.clientWidth*i.x,u=e.clientHeight*i.y;return{width:l,height:u,x:o*i.x,y:r*i.y}}(t,n);else{let n=Y(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return w(r)}function Z(e){return"static"===F(e).position}function J(e,t){if(!O(e)||"fixed"===F(e).position)return null;if(t)return t(e);let n=e.offsetParent;return T(e)===n&&(n=n.ownerDocument.body),n}function Q(e,t){let n=L(e);if(D(e))return n;if(!O(e)){let t=_(e);for(;t&&!I(t);){if(A(t)&&!Z(t))return t;t=_(t)}return n}let r=J(e,t);for(;r&&["table","td","th"].includes(k(r))&&Z(r);)r=J(r,t);return r&&I(r)&&Z(r)&&!P(r)?n:r||function(e){let t=_(e);for(;O(t)&&!I(t);){if(P(t))return t;if(D(t))break;t=_(t)}return null}(e)||n}let ee=async function(e){let t=this.getOffsetParent||Q,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=O(t),o=T(t),i="fixed"===n,l=G(e,!0,i,t),u={scrollLeft:0,scrollTop:0},c=a(0);if(r||!r&&!i)if(("body"!==k(t)||S(o))&&(u=W(t)),r){let e=G(t,!0,i,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else o&&(c.x=K(o));i&&!r&&o&&(c.x=K(o));let s=!o||r||i?a(0):q(o,u);return{x:l.left+u.scrollLeft-c.x-s.x,y:l.top+u.scrollTop-c.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},et={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=T(r),u=!!t&&D(t.floating);if(r===l||u&&i)return n;let c={scrollLeft:0,scrollTop:0},s=a(1),d=a(0),f=O(r);if((f||!f&&!i)&&(("body"!==k(r)||S(l))&&(c=W(r)),O(r))){let e=G(r);s=V(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!l||f||i?a(0):q(l,c,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-c.scrollLeft*s.x+d.x+p.x,y:n.y*s.y-c.scrollTop*s.y+d.y+p.y}},getDocumentElement:T,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:i,strategy:l}=e,a=[..."clippingAncestors"===n?D(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=B(e,[],!1).filter(e=>A(e)&&"body"!==k(e)),o=null,i="fixed"===F(e).position,l=i?_(e):e;for(;A(l)&&!I(l);){let t=F(l),n=P(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||S(l)&&!n&&function e(t,n){let r=_(t);return!(r===n||!A(r)||I(r))&&("fixed"===F(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=_(l)}return t.set(e,r),r}(t,this._c):[].concat(n),i],u=a[0],c=a.reduce((e,n)=>{let i=X(t,n,l);return e.top=o(i.top,e.top),e.right=r(i.right,e.right),e.bottom=r(i.bottom,e.bottom),e.left=o(i.left,e.left),e},X(t,u,l));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:Q,getElementRects:ee,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=U(e);return{width:t,height:n}},getScale:V,isElement:A,isRTL:function(e){return"rtl"===F(e).direction}};function en(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function er(e,t,n,i){let a;void 0===i&&(i={});let{ancestorScroll:u=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=i,p=z(e),m=u||c?[...p?B(p):[],...B(t)]:[];m.forEach(e=>{u&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let v=p&&d?function(e,t){let n,i=null,a=T(e);function u(){var e;clearTimeout(n),null==(e=i)||e.disconnect(),i=null}return!function c(s,d){void 0===s&&(s=!1),void 0===d&&(d=1),u();let f=e.getBoundingClientRect(),{left:p,top:m,width:v,height:h}=f;if(s||t(),!v||!h)return;let g=l(m),y=l(a.clientWidth-(p+v)),w={rootMargin:-g+"px "+-y+"px "+-l(a.clientHeight-(m+h))+"px "+-l(p)+"px",threshold:o(0,r(1,d))||1},b=!0;function E(t){let r=t[0].intersectionRatio;if(r!==d){if(!b)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||en(f,e.getBoundingClientRect())||c(),b=!1}try{i=new IntersectionObserver(E,{...w,root:a.ownerDocument})}catch(e){i=new IntersectionObserver(E,w)}i.observe(e)}(!0),u}(p,n):null,h=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?G(e):null;return f&&function t(){let r=G(e);y&&!en(y,r)&&n(),y=r,a=requestAnimationFrame(t)}(),n(),()=>{var e;m.forEach(e=>{u&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==v||v(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(a)}}let eo=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await R(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}},ei=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:i,placement:l}=t,{mainAxis:a=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...f}=s(e,t),m={x:n,y:i},h=await x(t,f),g=v(d(l)),y=p(g),w=m[y],b=m[g];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+h[e],i=w-h[t];w=o(n,r(w,i))}if(u){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=b+h[e],i=b-h[t];b=o(n,r(b,i))}let E=c.fn({...t,[y]:w,[g]:b});return{...E,data:{x:E.x-n,y:E.y-i,enabled:{[y]:a,[g]:u}}}}}},el=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:y,platform:w,elements:b}=t,{mainAxis:E=!0,crossAxis:R=!0,fallbackPlacements:C,fallbackStrategy:k="bestFit",fallbackAxisSideDirection:L="none",flipAlignment:T=!0,...N}=s(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let A=d(a),O=v(y),M=d(y)===y,S=await (null==w.isRTL?void 0:w.isRTL(b.floating)),D=C||(M||!T?[g(y)]:function(e){let t=g(e);return[h(e),t,h(t)]}(y)),P="none"!==L;!C&&P&&D.push(...function(e,t,n,r){let o=f(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(d(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(h)))),i}(y,T,L,S));let j=[y,...D],I=await x(t,N),F=[],W=(null==(r=u.flip)?void 0:r.overflows)||[];if(E&&F.push(I[A]),R){let e=function(e,t,n){void 0===n&&(n=!1);let r=f(e),o=p(v(e)),i=m(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=g(l)),[l,g(l)]}(a,c,S);F.push(I[e[0]],I[e[1]])}if(W=[...W,{placement:a,overflows:F}],!F.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=j[e];if(t&&("alignment"!==R||O===v(t)||W.every(e=>e.overflows[0]>0&&v(e.placement)===O)))return{data:{index:e,overflows:W},reset:{placement:t}};let n=null==(i=W.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(k){case"bestFit":{let e=null==(l=W.filter(e=>{if(P){let t=v(e.placement);return t===O||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=y}if(a!==n)return{reset:{placement:n}}}return{}}}},ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:i,placement:l,rects:a,platform:u,elements:c,middlewareData:d}=t,{element:h,padding:g=0}=s(e,t)||{};if(null==h)return{};let w=y(g),b={x:n,y:i},E=p(v(l)),x=m(E),R=await u.getDimensions(h),C="y"===E,k=C?"clientHeight":"clientWidth",L=a.reference[x]+a.reference[E]-b[E]-a.floating[x],T=b[E]-a.reference[E],N=await (null==u.getOffsetParent?void 0:u.getOffsetParent(h)),A=N?N[k]:0;A&&await (null==u.isElement?void 0:u.isElement(N))||(A=c.floating[k]||a.floating[x]);let O=A/2-R[x]/2-1,M=r(w[C?"top":"left"],O),S=r(w[C?"bottom":"right"],O),D=A-R[x]-S,P=A/2-R[x]/2+(L/2-T/2),j=o(M,r(P,D)),I=!d.arrow&&null!=f(l)&&P!==j&&a.reference[x]/2-(P<M?M:S)-R[x]/2<0,F=I?P<M?P-M:P-D:0;return{[E]:b[E]+F,data:{[E]:j,centerOffset:P-j-F,...I&&{alignmentOffset:F}},reset:I}}}),eu=(e,t,n)=>{let r=new Map,o={platform:et,...n},i={...o.platform,_c:r};return E(e,t,{...o,platform:i})}},24357:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},34869:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},47477:(e,t,n)=>{n.d(t,{bm:()=>to,UC:()=>tt,VY:()=>tr,hJ:()=>te,ZL:()=>e8,bL:()=>e6,hE:()=>tn,l9:()=>e3});var r,o,i,l=n(12115),a=n.t(l,2);function u(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var c=n(6101),s=n(95155),d=globalThis?.document?l.useLayoutEffect:()=>{},f=a[" useId ".trim().toString()]||(()=>void 0),p=0;function m(e){let[t,n]=l.useState(f());return d(()=>{e||n(e=>e??String(p++))},[e]),e||(t?`radix-${t}`:"")}a[" useEffectEvent ".trim().toString()],a[" useInsertionEffect ".trim().toString()];var v=a[" useInsertionEffect ".trim().toString()]||d,h=(Symbol("RADIX:SYNC_STATE"),n(63655));function g(e){let t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...e)=>t.current?.(...e),[])}var y="dismissableLayer.update",w=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),b=l.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:i=!1,onEscapeKeyDown:a,onPointerDownOutside:d,onFocusOutside:f,onInteractOutside:p,onDismiss:m,...v}=e,b=l.useContext(w),[R,C]=l.useState(null),k=null!=(r=null==R?void 0:R.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,L]=l.useState({}),T=(0,c.s)(t,e=>C(e)),N=Array.from(b.layers),[A]=[...b.layersWithOutsidePointerEventsDisabled].slice(-1),O=N.indexOf(A),M=R?N.indexOf(R):-1,S=b.layersWithOutsidePointerEventsDisabled.size>0,D=M>=O,P=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=g(e),o=l.useRef(!1),i=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){x("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...b.branches].some(e=>e.contains(t));D&&!n&&(null==d||d(e),null==p||p(e),e.defaultPrevented||null==m||m())},k),j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=g(e),o=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!o.current&&x("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...b.branches].some(e=>e.contains(t))&&(null==f||f(e),null==p||p(e),e.defaultPrevented||null==m||m())},k);return!function(e,t=globalThis?.document){let n=g(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{M===b.layers.size-1&&(null==a||a(e),!e.defaultPrevented&&m&&(e.preventDefault(),m()))},k),l.useEffect(()=>{if(R)return i&&(0===b.layersWithOutsidePointerEventsDisabled.size&&(o=k.body.style.pointerEvents,k.body.style.pointerEvents="none"),b.layersWithOutsidePointerEventsDisabled.add(R)),b.layers.add(R),E(),()=>{i&&1===b.layersWithOutsidePointerEventsDisabled.size&&(k.body.style.pointerEvents=o)}},[R,k,i,b]),l.useEffect(()=>()=>{R&&(b.layers.delete(R),b.layersWithOutsidePointerEventsDisabled.delete(R),E())},[R,b]),l.useEffect(()=>{let e=()=>L({});return document.addEventListener(y,e),()=>document.removeEventListener(y,e)},[]),(0,s.jsx)(h.sG.div,{...v,ref:T,style:{pointerEvents:S?D?"auto":"none":void 0,...e.style},onFocusCapture:u(e.onFocusCapture,j.onFocusCapture),onBlurCapture:u(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:u(e.onPointerDownCapture,P.onPointerDownCapture)})});function E(){let e=new CustomEvent(y);document.dispatchEvent(e)}function x(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,h.hO)(i,l):i.dispatchEvent(l)}b.displayName="DismissableLayer",l.forwardRef((e,t)=>{let n=l.useContext(w),r=l.useRef(null),o=(0,c.s)(t,r);return l.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(h.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var R="focusScope.autoFocusOnMount",C="focusScope.autoFocusOnUnmount",k={bubbles:!1,cancelable:!0},L=l.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...a}=e,[u,d]=l.useState(null),f=g(o),p=g(i),m=l.useRef(null),v=(0,c.s)(t,e=>d(e)),y=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let e=function(e){if(y.paused||!u)return;let t=e.target;u.contains(t)?m.current=t:A(m.current,{select:!0})},t=function(e){if(y.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||A(m.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&A(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,y.paused]),l.useEffect(()=>{if(u){O.add(y);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(R,k);u.addEventListener(R,f),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(A(r,{select:t}),document.activeElement!==n)return}(T(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&A(u))}return()=>{u.removeEventListener(R,f),setTimeout(()=>{let t=new CustomEvent(C,k);u.addEventListener(C,p),u.dispatchEvent(t),t.defaultPrevented||A(null!=e?e:document.body,{select:!0}),u.removeEventListener(C,p),O.remove(y)},0)}}},[u,f,p,y]);let w=l.useCallback(e=>{if(!n&&!r||y.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=T(e);return[N(t,e),N(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&A(i,{select:!0})):(e.preventDefault(),n&&A(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,y.paused]);return(0,s.jsx)(h.sG.div,{tabIndex:-1,...a,ref:v,onKeyDown:w})});function T(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function N(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function A(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}L.displayName="FocusScope";var O=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=M(e,t)).unshift(t)},remove(t){var n;null==(n=(e=M(e,t))[0])||n.resume()}}}();function M(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var S=n(47650),D=l.forwardRef((e,t)=>{var n,r;let{container:o,...i}=e,[a,u]=l.useState(!1);d(()=>u(!0),[]);let c=o||a&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return c?S.createPortal((0,s.jsx)(h.sG.div,{...i,ref:t}),c):null});D.displayName="Portal";var P=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,o]=l.useState(),i=l.useRef(null),a=l.useRef(e),u=l.useRef("none"),[c,s]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},l.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return l.useEffect(()=>{let e=j(i.current);u.current="mounted"===c?e:"none"},[c]),d(()=>{let t=i.current,n=a.current;if(n!==e){let r=u.current,o=j(t);e?s("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?s("UNMOUNT"):n&&r!==o?s("ANIMATION_OUT"):s("UNMOUNT"),a.current=e}},[e,s]),d(()=>{if(r){var e;let t,n=null!=(e=r.ownerDocument.defaultView)?e:window,o=e=>{let o=j(i.current).includes(e.animationName);if(e.target===r&&o&&(s("ANIMATION_END"),!a.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},l=e=>{e.target===r&&(u.current=j(i.current))};return r.addEventListener("animationstart",l),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",l),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}s("ANIMATION_END")},[r,s]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:l.useCallback(e=>{i.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof n?n({present:r.isPresent}):l.Children.only(n),i=(0,c.s)(r.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof n||r.isPresent?l.cloneElement(o,{ref:i}):null};function j(e){return(null==e?void 0:e.animationName)||"none"}P.displayName="Presence";var I=0;function F(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var W=function(){return(W=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function _(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var B=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),H="width-before-scroll-bar";function U(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var z="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,V=new WeakMap,$=function(){return null},Y=new WeakMap;function G(e){return e}new WeakMap;var K=function(e){var t=e.sideCar,n=_(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,W({},n))};K.isSideCarExport=!0;var q=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=G),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),r={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),r}}}});return i.options=W({async:!0,ssr:!1},e),i}(),X=function(){},Z=l.forwardRef(function(e,t){var n,r,o,i,a=l.useRef(null),u=l.useState({onScrollCapture:X,onWheelCapture:X,onTouchMoveCapture:X}),c=u[0],s=u[1],d=e.forwardProps,f=e.children,p=e.className,m=e.removeScrollBar,v=e.enabled,h=e.shards,g=e.sideCar,y=e.noRelative,w=e.noIsolation,b=e.inert,E=e.allowPinchZoom,x=e.as,R=e.gapMode,C=_(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),k=(n=[a,t],r=function(e){return n.forEach(function(t){return U(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,z(function(){var e=V.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||U(e,null)}),r.forEach(function(e){t.has(e)||U(e,o)})}V.set(i,n)},[n]),i),L=W(W({},C),c);return l.createElement(l.Fragment,null,v&&l.createElement(g,{sideCar:q,removeScrollBar:m,shards:h,noRelative:y,noIsolation:w,inert:b,setCallbacks:s,allowPinchZoom:!!E,lockRef:a,gapMode:R}),d?l.cloneElement(l.Children.only(f),W(W({},L),{ref:k})):l.createElement(void 0===x?"div":x,W({},L,{className:p,ref:k}),f))});Z.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},Z.classNames={fullWidth:H,zeroRight:B};var J=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,l;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Q=function(){var e=J();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},ee=function(){var e=Q();return function(t){return e(t.styles,t.dynamic),null}},et={left:0,top:0,right:0,gap:0},en=function(e){return parseInt(e||"",10)||0},er=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[en(n),en(r),en(o)]},eo=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return et;var t=er(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},ei=ee(),el="data-scroll-locked",ea=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(el,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(B," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(H," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(B," .").concat(B," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(H," .").concat(H," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(el,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},eu=function(){var e=parseInt(document.body.getAttribute(el)||"0",10);return isFinite(e)?e:0},ec=function(){l.useEffect(function(){return document.body.setAttribute(el,(eu()+1).toString()),function(){var e=eu()-1;e<=0?document.body.removeAttribute(el):document.body.setAttribute(el,e.toString())}},[])},es=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;ec();var i=l.useMemo(function(){return eo(o)},[o]);return l.createElement(ei,{styles:ea(i,!t,o,n?"":"!important")})},ed=!1;if("undefined"!=typeof window)try{var ef=Object.defineProperty({},"passive",{get:function(){return ed=!0,!0}});window.addEventListener("test",ef,ef),window.removeEventListener("test",ef,ef)}catch(e){ed=!1}var ep=!!ed&&{passive:!1},em=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},ev=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),eh(e,r)){var o=eg(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},eh=function(e,t){return"v"===e?em(t,"overflowY"):em(t,"overflowX")},eg=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},ey=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{if(!u)break;var m=eg(e,u),v=m[0],h=m[1]-m[2]-l*v;(v||h)&&eh(e,u)&&(f+=h,p+=v);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},ew=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},eb=function(e){return[e.deltaX,e.deltaY]},eE=function(e){return e&&"current"in e?e.current:e},ex=0,eR=[];let eC=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(ex++)[0],i=l.useState(ee)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(eE),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=ew(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=ev(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=ev(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return ey(p,t,e,"h"===p?u:c,!0)},[]),c=l.useCallback(function(e){if(eR.length&&eR[eR.length-1]===i){var n="deltaY"in e?eb(e):ew(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(eE).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=ew(e),r.current=void 0},[]),f=l.useCallback(function(t){s(t.type,eb(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,ew(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return eR.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,ep),document.addEventListener("touchmove",c,ep),document.addEventListener("touchstart",d,ep),function(){eR=eR.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,ep),document.removeEventListener("touchmove",c,ep),document.removeEventListener("touchstart",d,ep)}},[]);var m=e.removeScrollBar,v=e.inert;return l.createElement(l.Fragment,null,v?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?l.createElement(es,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},q.useMedium(r),K);var ek=l.forwardRef(function(e,t){return l.createElement(Z,W({},e,{ref:t,sideCar:eC}))});ek.classNames=Z.classNames;var eL=n(38168),eT=n(99708),eN="Dialog",[eA,eO]=function(e,t=[]){let n=[],r=()=>{let t=n.map(e=>l.createContext(e));return function(n){let r=n?.[e]||t;return l.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=l.createContext(r),i=n.length;n=[...n,r];let a=t=>{let{scope:n,children:r,...a}=t,u=n?.[e]?.[i]||o,c=l.useMemo(()=>a,Object.values(a));return(0,s.jsx)(u.Provider,{value:c,children:r})};return a.displayName=t+"Provider",[a,function(n,a){let u=a?.[e]?.[i]||o,c=l.useContext(u);if(c)return c;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return l.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}(eN),[eM,eS]=eA(eN),eD=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:i,modal:a=!0}=e,u=l.useRef(null),c=l.useRef(null),[d,f]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,i,a]=function({defaultProp:e,onChange:t}){let[n,r]=l.useState(e),o=l.useRef(n),i=l.useRef(t);return v(()=>{i.current=t},[t]),l.useEffect(()=>{o.current!==n&&(i.current?.(n),o.current=n)},[n,o]),[n,r,i]}({defaultProp:t,onChange:n}),u=void 0!==e,c=u?e:o;{let t=l.useRef(void 0!==e);l.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[c,l.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else i(t)},[u,e,i,a])]}({prop:r,defaultProp:null!=o&&o,onChange:i,caller:eN});return(0,s.jsx)(eM,{scope:t,triggerRef:u,contentRef:c,contentId:m(),titleId:m(),descriptionId:m(),open:d,onOpenChange:f,onOpenToggle:l.useCallback(()=>f(e=>!e),[f]),modal:a,children:n})};eD.displayName=eN;var eP="DialogTrigger",ej=l.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eS(eP,n),i=(0,c.s)(t,o.triggerRef);return(0,s.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":e1(o.open),...r,ref:i,onClick:u(e.onClick,o.onOpenToggle)})});ej.displayName=eP;var eI="DialogPortal",[eF,eW]=eA(eI,{forceMount:void 0}),e_=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:o}=e,i=eS(eI,t);return(0,s.jsx)(eF,{scope:t,forceMount:n,children:l.Children.map(r,e=>(0,s.jsx)(P,{present:n||i.open,children:(0,s.jsx)(D,{asChild:!0,container:o,children:e})}))})};e_.displayName=eI;var eB="DialogOverlay",eH=l.forwardRef((e,t)=>{let n=eW(eB,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=eS(eB,e.__scopeDialog);return i.modal?(0,s.jsx)(P,{present:r||i.open,children:(0,s.jsx)(ez,{...o,ref:t})}):null});eH.displayName=eB;var eU=(0,eT.TL)("DialogOverlay.RemoveScroll"),ez=l.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eS(eB,n);return(0,s.jsx)(ek,{as:eU,allowPinchZoom:!0,shards:[o.contentRef],children:(0,s.jsx)(h.sG.div,{"data-state":e1(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eV="DialogContent",e$=l.forwardRef((e,t)=>{let n=eW(eV,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=eS(eV,e.__scopeDialog);return(0,s.jsx)(P,{present:r||i.open,children:i.modal?(0,s.jsx)(eY,{...o,ref:t}):(0,s.jsx)(eG,{...o,ref:t})})});e$.displayName=eV;var eY=l.forwardRef((e,t)=>{let n=eS(eV,e.__scopeDialog),r=l.useRef(null),o=(0,c.s)(t,n.contentRef,r);return l.useEffect(()=>{let e=r.current;if(e)return(0,eL.Eq)(e)},[]),(0,s.jsx)(eK,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:u(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:u(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:u(e.onFocusOutside,e=>e.preventDefault())})}),eG=l.forwardRef((e,t)=>{let n=eS(eV,e.__scopeDialog),r=l.useRef(!1),o=l.useRef(!1);return(0,s.jsx)(eK,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var i,l;null==(i=e.onCloseAutoFocus)||i.call(e,t),t.defaultPrevented||(r.current||null==(l=n.triggerRef.current)||l.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{var i,l;null==(i=e.onInteractOutside)||i.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let a=t.target;(null==(l=n.triggerRef.current)?void 0:l.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),eK=l.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,...a}=e,u=eS(eV,n),d=l.useRef(null),f=(0,c.s)(t,d);return l.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:F()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:F()),I++,()=>{1===I&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),I--}},[]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(L,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,s.jsx)(b,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":e1(u.open),...a,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(e9,{titleId:u.titleId}),(0,s.jsx)(e7,{contentRef:d,descriptionId:u.descriptionId})]})]})}),eq="DialogTitle",eX=l.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eS(eq,n);return(0,s.jsx)(h.sG.h2,{id:o.titleId,...r,ref:t})});eX.displayName=eq;var eZ="DialogDescription",eJ=l.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eS(eZ,n);return(0,s.jsx)(h.sG.p,{id:o.descriptionId,...r,ref:t})});eJ.displayName=eZ;var eQ="DialogClose",e0=l.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eS(eQ,n);return(0,s.jsx)(h.sG.button,{type:"button",...r,ref:t,onClick:u(e.onClick,()=>o.onOpenChange(!1))})});function e1(e){return e?"open":"closed"}e0.displayName=eQ;var e2="DialogTitleWarning",[e4,e5]=function(e,t){let n=l.createContext(t),r=e=>{let{children:t,...r}=e,o=l.useMemo(()=>r,Object.values(r));return(0,s.jsx)(n.Provider,{value:o,children:t})};return r.displayName=e+"Provider",[r,function(r){let o=l.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${r}\` must be used within \`${e}\``)}]}(e2,{contentName:eV,titleName:eq,docsSlug:"dialog"}),e9=e=>{let{titleId:t}=e,n=e5(e2),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return l.useEffect(()=>{t&&document.getElementById(t)},[r,t]),null},e7=e=>{let{contentRef:t,descriptionId:n}=e,r=e5("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return l.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&document.getElementById(n)},[o,t,n]),null},e6=eD,e3=ej,e8=e_,te=eH,tt=e$,tn=eX,tr=eJ,to=e0},53904:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},62525:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},69074:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},69803:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},75525:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},78749:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},79397:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},84616:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},89959:(e,t,n)=>{n.d(t,{A:()=>o});var r=n(12115);let o=r.forwardRef(function(e,t){let{title:n,titleId:o,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},i),n?r.createElement("title",{id:o},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9.75v6.75m0 0-3-3m3 3 3-3m-8.25 6a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18 19.5H6.75Z"}))})},92657:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);