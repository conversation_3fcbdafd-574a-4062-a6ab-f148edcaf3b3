(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4150],{574:(e,t,a)=>{"use strict";a.d(t,{m:()=>r.m});var r=a(76804)},14362:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>P});var r=a(95155),s=a(12115),i=a(35695),l=a(75922);let n=[{id:"general_chat",name:"<PERSON> Chat",description:"Casual conversation, simple questions, greetings, and basic interactions that do not require specialized expertise."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"Mathematical calculations, logical puzzles, analytical problem-solving, deductive reasoning, and complex analytical thinking."},{id:"writing",name:"Writing & Content Creation",description:"Creating written content like stories, articles, books, poems, scripts, marketing copy, blog posts, and creative narratives."},{id:"coding_frontend",name:"Coding - Frontend",description:"Building user interfaces with HTML, CSS, JavaScript, React, Vue, Angular, web design, and client-side development."},{id:"coding_backend",name:"Coding - Backend",description:"Programming server-side applications, APIs, databases, Python scripts, Node.js, Java, backend logic, and system architecture."},{id:"research_synthesis",name:"Research & Synthesis",description:"Gathering information from sources, conducting research, analyzing data, and synthesizing findings into comprehensive reports."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"Condensing lengthy documents, extracting key points, creating executive summaries, and briefing complex information."},{id:"translation_localization",name:"Translation & Localization",description:"Converting text between different languages, linguistic translation, cultural adaptation, and multilingual communication."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"Extracting specific information from unstructured text, organizing data into tables, lists, JSON, CSV, and structured formats."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"Generating creative ideas, innovative concepts, brainstorming solutions, exploring possibilities, and creative thinking sessions."},{id:"education_tutoring",name:"Education & Tutoring",description:"Teaching concepts, explaining topics step-by-step, creating educational materials, tutoring, and providing learning guidance."},{id:"image_generation",name:"Image Generation",description:"Creating visual images, artwork, graphics, and illustrations from textual descriptions using AI image generation models."},{id:"audio_transcription",name:"Audio Transcription",description:"Converting speech from audio files into written text, transcribing recordings, and speech-to-text processing."},{id:"data_extractor",name:"Data Extractor",description:"Extracting specific data from web pages, scraping content, and gathering information from websites."},{id:"form_filler",name:"Form Filler",description:"Filling out web forms, submitting data, and handling form-based interactions on websites."},{id:"verification_agent",name:"Verification Agent",description:"Verifying information on websites, fact-checking, and validating data accuracy."},{id:"research_assistant",name:"Research Assistant",description:"Conducting web-based research, gathering information from multiple sources, and compiling research findings."},{id:"shopping_assistant",name:"Shopping Assistant",description:"Helping with online shopping, price comparisons, product research, and e-commerce tasks."},{id:"price_comparison",name:"Price Comparison",description:"Comparing prices across different websites, finding deals, and analyzing product pricing."},{id:"fact_checker",name:"Fact Checker",description:"Verifying facts and information across multiple web sources, cross-referencing data for accuracy."},{id:"task_executor",name:"Task Executor",description:"General task execution and automation, handling various web-based tasks and workflows."}],o=e=>n.find(t=>t.id===e);var d=a(32461),c=a(6865),u=a(89959),m=a(37186),g=(a(55628),a(65529)),x=a(67695),p=a(94038),h=a(61316),f=a(85037),b=a(57765),y=a(8246),v=a(31151),j=a(52589),w=a(55424),N=a(80377),k=a(87162),_=a(28003),C=a(79958),A=a(53951),I=a(99323),S=a(35287),T=a(60993);let D=l.MG.map(e=>({value:e.id,label:e.name}));function P(){var e,t;let a=(0,i.useParams)().configId,P=(0,k.Z)(),E=(0,I.bu)(),R=(null==E?void 0:E.navigateOptimistically)||(e=>{window.location.href=e}),{getCachedData:M,isCached:F,clearCache:O}=(0,_._)(),{createHoverPrefetch:K}=(0,A.c)(),[L,V]=(0,s.useState)(null),[z,B]=(0,s.useState)(!0),[G,U]=(0,s.useState)(!1),[J,q]=(0,s.useState)((null==(e=D[0])?void 0:e.value)||"openai"),[H,W]=(0,s.useState)(""),[Z,Q]=(0,s.useState)(""),[X,Y]=(0,s.useState)(""),[$,ee]=(0,s.useState)(1),[et,ea]=(0,s.useState)(!1),[er,es]=(0,s.useState)(null),[ei,el]=(0,s.useState)(null),[en,eo]=(0,s.useState)(null),[ed,ec]=(0,s.useState)(!1),[eu,em]=(0,s.useState)(null),[eg,ex]=(0,s.useState)([]),[ep,eh]=(0,s.useState)(!0),[ef,eb]=(0,s.useState)(null),[ey,ev]=(0,s.useState)(null),[ej,ew]=(0,s.useState)(null),[eN,ek]=(0,s.useState)(null),[e_,eC]=(0,s.useState)(1),[eA,eI]=(0,s.useState)(""),[eS,eT]=(0,s.useState)(!1),[eD,eP]=(0,s.useState)([]),[eE,eR]=(0,s.useState)(!1),[eM,eF]=(0,s.useState)(null),[eO,eK]=(0,s.useState)(!1),[eL,eV]=(0,s.useState)(""),[ez,eB]=(0,s.useState)(""),[eG,eU]=(0,s.useState)(""),[eJ,eq]=(0,s.useState)(!1),[eH,eW]=(0,s.useState)(null),[eZ,eQ]=(0,s.useState)(null),[eX,eY]=(0,s.useState)("provider-keys"),[e$,e0]=(0,s.useState)(!1),[e5,e2]=(0,s.useState)([]),[e4,e1]=(0,s.useState)(!1),e3=(0,s.useCallback)(async()=>{if(!a)return;let e=M(a);if(e&&e.configDetails){V(e.configDetails),void 0!==e.configDetails.browsing_enabled&&e0(e.configDetails.browsing_enabled),e.configDetails.browsing_models&&e2(e.configDetails.browsing_models),B(!1);return}F(a)||U(!0),B(!0),es(null);try{let e=await fetch("/api/custom-configs");if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to fetch configurations list")}let t=(await e.json()).find(e=>e.id===a);if(!t)throw Error("Configuration not found in the list.");V(t),void 0!==t.browsing_enabled&&e0(t.browsing_enabled),t.browsing_models&&e2(t.browsing_models)}catch(e){es("Error loading model configuration: ".concat(e.message)),V(null)}finally{B(!1),U(!1)}},[a,M,F]);(0,s.useEffect)(()=>{e3()},[e3]);let e6=(0,s.useCallback)(async()=>{let e=M(a);if(e&&e.models){eo(e.models),ec(!1);return}ec(!0),em(null),eo(null);try{let e=await fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({})}),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to fetch models from database.");t.models?eo(t.models):eo([])}catch(e){em("Error fetching models: ".concat(e.message)),eo([])}finally{ec(!1)}},[a,M]);(0,s.useEffect)(()=>{a&&e6()},[a,e6]);let e8=(0,s.useCallback)(async()=>{let e=M(a);if(e&&e.userCustomRoles){eP(e.userCustomRoles),eR(!1);return}eR(!0),eF(null);try{let e=await fetch("/api/user/custom-roles");if(e.ok){let t=await e.json();eP(t)}else{let t;try{t=await e.json()}catch(a){t={error:await e.text().catch(()=>"HTTP error ".concat(e.status))}}let a=t.error||(t.issues?JSON.stringify(t.issues):"Failed to fetch custom roles (status: ".concat(e.status,")"));if(401===e.status)eF(a);else throw Error(a);eP([])}}catch(e){eF(e.message),eP([])}finally{eR(!1)}},[]),e9=(0,s.useCallback)(async()=>{if(!a||!eD)return;let e=M(a);if(e&&e.apiKeys&&void 0!==e.defaultChatKeyId){let t=e.apiKeys.map(async t=>{let a=await fetch("/api/keys/".concat(t.id,"/roles")),r=[];return a.ok&&(r=(await a.json()).map(e=>{let t=o(e.role_name);if(t)return t;let a=eD.find(t=>t.role_id===e.role_name);return a?{id:a.role_id,name:a.name,description:a.description||void 0}:null}).filter(Boolean)),{...t,assigned_roles:r,is_default_general_chat_model:e.defaultChatKeyId===t.id}});ex(await Promise.all(t)),ev(e.defaultChatKeyId),eh(!1);return}eh(!0),es(e=>e&&e.startsWith("Error loading model configuration:")?e:null),el(null);try{let e=await fetch("/api/keys?custom_config_id=".concat(a));if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to fetch API keys")}let t=await e.json(),r=await fetch("/api/custom-configs/".concat(a,"/default-chat-key"));r.ok;let s=200===r.status?await r.json():null;ev((null==s?void 0:s.id)||null);let i=t.map(async e=>{let t=await fetch("/api/keys/".concat(e.id,"/roles")),a=[];return t.ok&&(a=(await t.json()).map(e=>{let t=o(e.role_name);if(t)return t;let a=eD.find(t=>t.role_id===e.role_name);return a?{id:a.role_id,name:a.name,description:a.description||void 0}:null}).filter(Boolean)),{...e,assigned_roles:a,is_default_general_chat_model:(null==s?void 0:s.id)===e.id}}),l=await Promise.all(i);ex(l)}catch(e){es(t=>t?"".concat(t,"; ").concat(e.message):e.message)}finally{eh(!1)}},[a,eD]);(0,s.useEffect)(()=>{L&&e8()},[L,e8]),(0,s.useEffect)(()=>{L&&eD&&e9()},[L,eD,e9]);let e7=(0,s.useMemo)(()=>{if(en){let e=l.MG.find(e=>e.id===J);if(!e)return[];if("openrouter"===e.id)return en.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===e.id){let e=[];return en.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),en.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return en.filter(t=>t.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[en,J]),te=(0,s.useMemo)(()=>{if(en&&eN){let e=l.MG.find(e=>e.id===eN.provider);if(!e)return[];if("openrouter"===e.id)return en.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===e.id){let e=[];return en.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),en.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return en.filter(t=>t.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[en,eN]);(0,s.useCallback)(e=>{if(!en)return[];let t=l.MG.find(t=>t.id===e);if(!t)return[];if("openrouter"===t.id)return en.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===t.id){let e=["deepseek-chat","deepseek-reasoner"];return en.filter(t=>"deepseek"===t.provider_id&&e.some(e=>t.id.includes(e))).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return en.filter(e=>e.provider_id===t.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))},[en]),(0,s.useEffect)(()=>{e7.length>0?W(e7[0].value):W("")},[e7,J]),(0,s.useEffect)(()=>{J&&e6()},[J,e6]);let tt=async e=>{if(e.preventDefault(),!a)return void es("Configuration ID is missing.");if(eg.some(e=>e.predefined_model_id===H))return void es("This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.");ea(!0),es(null),el(null);let t=[...eg];try{var r;let e=await fetch("/api/keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({custom_api_config_id:a,provider:J,predefined_model_id:H,api_key_raw:Z,label:X,temperature:$})}),t=await e.json();if(!e.ok)throw Error(t.details||t.error||"Failed to save API key");let s={id:t.id,custom_api_config_id:a,provider:J,predefined_model_id:H,label:X,temperature:$,status:"active",created_at:new Date().toISOString(),last_used_at:null,is_default_general_chat_model:!1,assigned_roles:[]};ex(e=>[...e,s]),O(a),el('API key "'.concat(X,'" saved successfully!')),q((null==(r=D[0])?void 0:r.value)||"openai"),Q(""),Y(""),ee(1),e7.length>0&&W(e7[0].value)}catch(e){ex(t),es("Save Key Error: ".concat(e.message))}finally{ea(!1)}},ta=e=>{ek(e),eC(e.temperature||1),eI(e.predefined_model_id)},tr=async()=>{if(!eN)return;if(eg.some(e=>e.id!==eN.id&&e.predefined_model_id===eA))return void es("This model is already configured in this setup. Each model can only be used once per configuration.");eT(!0),es(null),el(null);let e=[...eg];ex(e=>e.map(e=>e.id===eN.id?{...e,temperature:e_,predefined_model_id:eA}:e));try{let t=await fetch("/api/keys?id=".concat(eN.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({temperature:e_,predefined_model_id:eA})}),r=await t.json();if(!t.ok)throw ex(e),Error(r.details||r.error||"Failed to update API key");O(a),el('API key "'.concat(eN.label,'" updated successfully!')),ek(null)}catch(e){es("Update Key Error: ".concat(e.message))}finally{eT(!1)}},ts=(e,t)=>{P.showConfirmation({title:"Delete API Key",message:'Are you sure you want to delete the API key "'.concat(t,'"? This will permanently remove the key and unassign all its roles. This action cannot be undone.'),confirmText:"Delete API Key",cancelText:"Cancel",type:"danger"},async()=>{eb(e),es(null),el(null);let r=[...eg],s=eg.find(t=>t.id===e);ex(t=>t.filter(t=>t.id!==e)),(null==s?void 0:s.is_default_general_chat_model)&&ev(null);try{let s=await fetch("/api/keys/".concat(e),{method:"DELETE"}),i=await s.json();if(!s.ok){if(ex(r),ev(ey),404===s.status){ex(t=>t.filter(t=>t.id!==e)),el('API key "'.concat(t,'" was already deleted.'));return}throw Error(i.details||i.error||"Failed to delete API key")}O(a),el('API key "'.concat(t,'" deleted successfully!'))}catch(e){throw es("Delete Key Error: ".concat(e.message)),e}finally{eb(null)}})},ti=async e=>{if(!a)return;es(null),el(null);let t=[...eg];ex(t=>t.map(t=>({...t,is_default_general_chat_model:t.id===e}))),ev(e);try{let r=await fetch("/api/custom-configs/".concat(a,"/default-key-handler/").concat(e),{method:"PUT"}),s=await r.json();if(!r.ok)throw ex(t.map(e=>({...e}))),ev(ey),Error(s.details||s.error||"Failed to set default chat key");O(a),el(s.message||"Default general chat key updated!")}catch(e){es("Set Default Error: ".concat(e.message))}},tl=async(e,t,a)=>{es(null),el(null);let r="/api/keys/".concat(e.id,"/roles"),s=[...n.map(e=>({...e,isCustom:!1})),...eD.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].find(e=>e.id===t)||{id:t,name:t,description:""},i=eg.map(e=>({...e,assigned_roles:[...e.assigned_roles.map(e=>({...e}))]})),l=null;ej&&ej.id===e.id&&(l={...ej,assigned_roles:[...ej.assigned_roles.map(e=>({...e}))]}),ex(r=>r.map(r=>{if(r.id===e.id){let e=a?r.assigned_roles.filter(e=>e.id!==t):[...r.assigned_roles,s];return{...r,assigned_roles:e}}return r})),ej&&ej.id===e.id&&ew(e=>{if(!e)return null;let r=a?e.assigned_roles.filter(e=>e.id!==t):[...e.assigned_roles,s];return{...e,assigned_roles:r}});try{let n;n=a?await fetch("".concat(r,"/").concat(t),{method:"DELETE"}):await fetch(r,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_name:t})});let o=await n.json();if(!n.ok){if(ex(i),l)ew(l);else if(ej&&ej.id===e.id){let t=i.find(t=>t.id===e.id);t&&ew(t)}let t=409===n.status&&o.error?o.error:o.details||o.error||(a?"Failed to unassign role":"Failed to assign role");throw Error(t)}el(o.message||"Role '".concat(s.name,"' ").concat(a?"unassigned":"assigned"," successfully."))}catch(e){es("Role Update Error: ".concat(e.message))}},tn=async()=>{if(!eL.trim()||eL.trim().length>30||!/^[a-zA-Z0-9_]+$/.test(eL.trim()))return void eW("Role ID is required (max 30 chars, letters, numbers, underscores only).");if(n.some(e=>e.id.toLowerCase()===eL.trim().toLowerCase())||eD.some(e=>e.role_id.toLowerCase()===eL.trim().toLowerCase()))return void eW("This Role ID is already in use (either predefined or as one of your custom roles).");if(!ez.trim())return void eW("Role Name is required.");eW(null),eq(!0);try{let e=await fetch("/api/user/custom-roles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_id:eL.trim(),name:ez.trim(),description:eG.trim()})});if(!e.ok){let t;try{t=await e.json()}catch(r){let a=await e.text().catch(()=>"HTTP status ".concat(e.status));t={error:"Server error, could not parse response.",details:a}}let a=t.error||"Failed to create custom role.";if(t.details)a+=" (Details: ".concat(t.details,")");else if(t.issues){let e=Object.entries(t.issues).map(e=>{let[t,a]=e;return"".concat(t,": ").concat(a.join(", "))}).join("; ");a+=" (Issues: ".concat(e,")")}throw Error(a)}let t=await e.json();eV(""),eB(""),eU(""),O(a);let r={id:t.id,role_id:t.role_id,name:t.name,description:t.description,user_id:t.user_id,created_at:t.created_at,updated_at:t.updated_at};eP(e=>[...e,r]),el("Custom role '".concat(t.name,"' created successfully! It is now available globally."))}catch(e){eW(e.message)}finally{eq(!1)}},to=(e,t)=>{e&&P.showConfirmation({title:"Delete Custom Role",message:'Are you sure you want to delete the custom role "'.concat(t,"\"? This will unassign it from all API keys where it's currently used. This action cannot be undone."),confirmText:"Delete Role",cancelText:"Cancel",type:"danger"},async()=>{eQ(e),eF(null),eW(null),el(null);try{let r=await fetch("/api/user/custom-roles/".concat(e),{method:"DELETE"}),s=await r.json();if(!r.ok)throw Error(s.error||"Failed to delete custom role");eP(t=>t.filter(t=>t.id!==e)),O(a),el(s.message||'Global custom role "'.concat(t,'" deleted successfully.')),a&&e9()}catch(e){throw eF("Error deleting role: ".concat(e.message)),e}finally{eQ(null)}})};return G&&!F(a)?(0,r.jsx)(C.A,{}):z&&!L?(0,r.jsx)(C._,{}):(0,r.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:(0,r.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("button",{onClick:()=>R("/my-models"),className:"text-orange-400 hover:text-orange-300 inline-flex items-center mb-6 transition-colors duration-200 group",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform"}),"Back to My API Models"]}),(0,r.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-8 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6",children:[(0,r.jsx)("div",{className:"flex-1",children:L?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg",children:(0,r.jsx)(m.A,{className:"h-6 w-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white",children:L.name}),(0,r.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"Model Configuration"})]})]}),(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-300 bg-gray-800/50 px-4 py-2 rounded-lg w-fit",children:[(0,r.jsx)("span",{className:"inline-block w-2 h-2 bg-orange-500 rounded-full mr-2"}),"ID: ",L.id]})]}):er&&!z?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-red-900/50 rounded-2xl flex items-center justify-center mr-4",children:(0,r.jsx)(j.A,{className:"h-6 w-6 text-red-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-red-400",children:"Configuration Error"}),(0,r.jsx)("p",{className:"text-red-300 mt-1",children:er.replace("Error loading model configuration: ","")})]})]}):(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-800/50 rounded-2xl flex items-center justify-center mr-4",children:(0,r.jsx)(u.A,{className:"h-6 w-6 text-gray-400 animate-pulse"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Loading Configuration..."}),(0,r.jsx)("p",{className:"text-gray-400 mt-1",children:"Please wait while we fetch your model details"})]})]})}),L&&(0,r.jsx)("div",{className:"flex flex-col sm:flex-row gap-3",children:(0,r.jsxs)("button",{onClick:()=>R("/routing-setup/".concat(a,"?from=model-config")),className:"inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group",...K(a),children:[(0,r.jsx)(m.A,{className:"h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200"}),"Advanced Routing Setup"]})})]})}),ei&&(0,r.jsx)("div",{className:"bg-green-900/50 backdrop-blur-sm border border-green-800/50 rounded-lg p-4 mb-6 animate-slide-in",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 text-green-400"}),(0,r.jsx)("p",{className:"text-green-300 font-medium",children:ei})]})}),er&&(0,r.jsx)("div",{className:"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4 mb-6 animate-slide-in",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(j.A,{className:"h-5 w-5 text-red-400"}),(0,r.jsx)("p",{className:"text-red-300 font-medium",children:er})]})})]}),L&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-2",children:(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("button",{onClick:()=>eY("provider-keys"),className:"flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ".concat("provider-keys"===eX?"bg-orange-500 text-white shadow-md":"text-gray-400 hover:text-white hover:bg-gray-800/50"),children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Provider API Keys"})]})}),(0,r.jsx)("button",{onClick:()=>eY("user-api-keys"),className:"flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ".concat("user-api-keys"===eX?"bg-orange-500 text-white shadow-md":"text-gray-400 hover:text-white hover:bg-gray-800/50"),children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Generated API Keys"})]})}),!1]})}),"provider-keys"===eX&&(0,r.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-5 gap-8",children:[(0,r.jsx)("div",{className:"xl:col-span-2",children:(0,r.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 sticky top-8",children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,r.jsx)(b.A,{className:"h-5 w-5 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-white",children:"Add Provider API Key"}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"Configure new provider key"})]})]}),(0,r.jsxs)("form",{onSubmit:tt,className:"space-y-5",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"provider",className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,r.jsx)("select",{id:"provider",value:J,onChange:e=>{q(e.target.value)},className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm",children:D.map(e=>(0,r.jsx)("option",{value:e.value,className:"bg-gray-800 text-white",children:e.label},e.value))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"apiKeyRaw",className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key"}),(0,r.jsx)("input",{id:"apiKeyRaw",type:"password",value:Z,onChange:e=>Q(e.target.value),className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm",placeholder:"Enter your API key"}),ed&&null===en&&(0,r.jsxs)("p",{className:"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-1 animate-pulse"}),"Fetching models..."]}),eu&&(0,r.jsx)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:eu})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"predefinedModelId",className:"block text-sm font-medium text-gray-300 mb-2",children:"Model Variant"}),(0,r.jsx)("select",{id:"predefinedModelId",value:H,onChange:e=>W(e.target.value),disabled:!e7.length,className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm disabled:bg-gray-800/30 disabled:text-gray-500",children:e7.length>0?e7.map(e=>(0,r.jsx)("option",{value:e.value,className:"bg-gray-800 text-white",children:e.label},e.value)):(0,r.jsx)("option",{value:"",disabled:!0,className:"bg-gray-800 text-gray-400",children:null===en&&ed?"Loading models...":"Select a provider first"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"label",className:"block text-sm font-medium text-gray-300 mb-2",children:"Label"}),(0,r.jsx)("input",{type:"text",id:"label",value:X,onChange:e=>Y(e.target.value),required:!0,className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm",placeholder:"e.g., My OpenAI GPT-4o Key #1"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature",(0,r.jsx)("span",{className:"text-xs text-gray-400 ml-1",children:"(0.0 - 2.0)"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:$,onChange:e=>ee(parseFloat(e.target.value)),className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-xs text-gray-400",children:"Conservative"}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsx)("input",{type:"number",min:"0",max:"2",step:"0.1",value:$,onChange:e=>ee(Math.min(2,Math.max(0,parseFloat(e.target.value)||0))),className:"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"})}),(0,r.jsx)("span",{className:"text-xs text-gray-400",children:"Creative"})]}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]})]}),(0,r.jsx)("button",{type:"submit",disabled:et||!H||""===H||!Z.trim()||!X.trim(),className:"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm",children:et?(0,r.jsxs)("span",{className:"flex items-center justify-center",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2 animate-pulse"}),"Saving..."]}):(0,r.jsxs)("span",{className:"flex items-center justify-center",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Add API Key"]})})]}),(0,r.jsx)("div",{className:"mt-6 p-4 bg-blue-900/50 backdrop-blur-sm border border-blue-800/50 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(x.A,{className:"h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-blue-300 mb-1",children:"Key Configuration Rules"}),(0,r.jsxs)("div",{className:"text-xs text-blue-200 space-y-1",children:[(0,r.jsxs)("p",{children:["✅ ",(0,r.jsx)("strong",{children:"Same API key, different models:"})," Allowed"]}),(0,r.jsxs)("p",{children:["✅ ",(0,r.jsx)("strong",{children:"Different API keys, same model:"})," Allowed"]}),(0,r.jsxs)("p",{children:["❌ ",(0,r.jsx)("strong",{children:"Same model twice:"})," Not allowed in one configuration"]})]})]})]})})]})}),(0,r.jsx)("div",{className:"xl:col-span-3",children:(0,r.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,r.jsx)(p.A,{className:"h-5 w-5 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-white",children:"API Keys & Roles"}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"Manage existing keys"})]})]}),ep&&(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"Loading API keys..."})]}),!ep&&0===eg.length&&(!er||er&&er.startsWith("Error loading model configuration:"))&&(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)(p.A,{className:"h-6 w-6 text-gray-400"})}),(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 mb-1",children:"No API Keys"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Add your first key using the form"})]}),!ep&&eg.length>0&&(0,r.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:eg.map((e,t)=>(0,r.jsx)("div",{className:"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 hover:border-gray-600/50 transition-all duration-200 animate-slide-in",style:{animationDelay:"".concat(50*t,"ms")},children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-white truncate mr-2",children:e.label}),e.is_default_general_chat_model&&(0,r.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-900/30 text-green-300 border border-green-500/50 flex-shrink-0",children:[(0,r.jsx)(y.A,{className:"h-3 w-3 mr-1"}),"Default"]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("p",{className:"text-xs text-gray-200 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600",children:[e.provider," (",e.predefined_model_id,")"]}),(0,r.jsxs)("p",{className:"text-xs text-orange-300 bg-orange-900/30 px-2 py-1 rounded-lg border border-orange-500/50",children:["Temp: ",e.temperature]})]}),(0,r.jsx)(T.sU,{feature:"custom_roles",fallback:(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:(0,r.jsx)("span",{className:"text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600",children:"Roles available on Starter plan+"})}),children:(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:e.assigned_roles.length>0?e.assigned_roles.map(e=>(0,r.jsx)("span",{className:"inline-block whitespace-nowrap rounded-full bg-orange-900/30 px-2 py-1 text-xs font-medium text-orange-300 border border-orange-500/50",children:e.name},e.id)):(0,r.jsx)("span",{className:"text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600",children:"No roles"})})})]}),!e.is_default_general_chat_model&&(0,r.jsx)("button",{onClick:()=>ti(e.id),className:"text-xs bg-gray-700/50 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white py-1 px-2 rounded-lg mt-2 transition-colors","data-tooltip-id":"global-tooltip","data-tooltip-content":"Set as default chat model",children:"Set Default"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 ml-2 flex-shrink-0",children:[(0,r.jsx)("button",{onClick:()=>ta(e),disabled:ef===e.id,className:"p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-900/30 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Edit Model & Settings",children:(0,r.jsx)(h.A,{className:"h-4 w-4"})}),(0,r.jsx)(T.sU,{feature:"custom_roles",fallback:(0,r.jsx)("button",{disabled:!0,className:"p-2 text-gray-400 cursor-not-allowed rounded-lg opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Role management requires Starter plan or higher",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})}),children:(0,r.jsx)("button",{onClick:()=>ew(e),disabled:ef===e.id,className:"p-2 text-orange-400 hover:text-orange-300 hover:bg-orange-900/30 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Manage Roles",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})})}),(0,r.jsx)("button",{onClick:()=>ts(e.id,e.label),disabled:ef===e.id,className:"p-2 text-red-400 hover:text-red-300 hover:bg-red-900/30 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Delete Key",children:ef===e.id?(0,r.jsx)(v.A,{className:"h-4 w-4 animate-pulse"}):(0,r.jsx)(v.A,{className:"h-4 w-4"})})]})]})},e.id))}),!ep&&er&&!er.startsWith("Error loading model configuration:")&&(0,r.jsx)("div",{className:"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(j.A,{className:"h-5 w-5 text-red-400"}),(0,r.jsxs)("p",{className:"text-red-300 font-medium text-sm",children:["Could not load API keys/roles: ",er]})]})})]})})]}),"user-api-keys"===eX&&(0,r.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:(0,r.jsx)(S.z,{configId:a,configName:L.name})}),"browsing-config"===eX&&!1]}),ej&&(()=>{if(!ej)return null;let e=[...n.map(e=>({...e,isCustom:!1})),...eD.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].sort((e,t)=>e.name.localeCompare(t.name));return(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,r.jsxs)("div",{className:"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg max-h-[90vh] flex flex-col",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-800/50",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-white",children:["Manage Roles for: ",(0,r.jsx)("span",{className:"text-orange-400",children:ej.label})]}),(0,r.jsx)("button",{onClick:()=>{ew(null),eK(!1),eW(null)},className:"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200",children:(0,r.jsx)(j.A,{className:"h-6 w-6"})})]}),(0,r.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[eM&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,r.jsxs)("p",{className:"text-red-800 text-sm",children:["Error with custom roles: ",eM]})}),(0,r.jsxs)(T.sU,{feature:"custom_roles",customMessage:"Custom roles are available starting with the Starter plan. Create specialized roles to organize your API keys by task type and optimize routing for different use cases.",children:[(0,r.jsx)("div",{className:"flex justify-end mb-4",children:(0,r.jsxs)("button",{onClick:()=>eK(!eO),className:"btn-primary text-sm inline-flex items-center",children:[(0,r.jsx)(f.A,{className:"h-4 w-4 mr-2"}),eO?"Cancel New Role":"Create New Custom Role"]})}),eO&&(0,r.jsxs)("div",{className:"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 mb-4",children:[(0,r.jsx)("h3",{className:"text-md font-medium text-white mb-3",children:"Create New Custom Role for this Configuration"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"newCustomRoleId",className:"block text-sm font-medium text-gray-300 mb-1",children:"Role ID (short, no spaces, max 30 chars)"}),(0,r.jsx)("input",{type:"text",id:"newCustomRoleId",value:eL,onChange:e=>eV(e.target.value.replace(/\s/g,"")),className:"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",maxLength:30,placeholder:"e.g., my_blog_writer"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"newCustomRoleName",className:"block text-sm font-medium text-gray-300 mb-1",children:"Display Name (max 100 chars)"}),(0,r.jsx)("input",{type:"text",id:"newCustomRoleName",value:ez,onChange:e=>eB(e.target.value),className:"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",maxLength:100,placeholder:"e.g., My Awesome Blog Writer"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"newCustomRoleDescription",className:"block text-sm font-medium text-gray-300 mb-1",children:"Description (optional, max 500 chars)"}),(0,r.jsx)("textarea",{id:"newCustomRoleDescription",value:eG,onChange:e=>eU(e.target.value),rows:2,className:"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",maxLength:500,placeholder:"Optional: Describe what this role is for..."})]}),eH&&(0,r.jsx)("div",{className:"bg-red-900/50 border border-red-800/50 rounded-lg p-3",children:(0,r.jsx)("p",{className:"text-red-300 text-sm",children:eH})}),(0,r.jsx)("button",{onClick:tn,disabled:eJ,className:"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed",children:eJ?"Saving Role...":"Save Custom Role"})]})]})]})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-300 mb-3",children:"Select roles to assign:"}),(0,r.jsxs)("div",{className:"overflow-y-auto space-y-2",style:{maxHeight:"calc(90vh - 350px)"},children:[eE&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-500"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm ml-2",children:"Loading custom roles..."})]}),e.map(e=>{let t=ej.assigned_roles.some(t=>t.id===e.id);return(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ".concat(t?"bg-orange-500/20 border-orange-500/30 shadow-sm":"bg-gray-800/50 border-gray-700/50 hover:border-gray-600/50 hover:shadow-sm"),children:[(0,r.jsxs)("label",{htmlFor:"role-".concat(e.id),className:"flex items-center cursor-pointer flex-grow",children:[(0,r.jsx)("input",{type:"checkbox",id:"role-".concat(e.id),checked:t,onChange:()=>tl(ej,e.id,t),className:"h-4 w-4 text-orange-500 border-gray-600 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer bg-gray-700"}),(0,r.jsx)("span",{className:"ml-3 text-sm font-medium ".concat(t?"text-orange-300":"text-white"),children:e.name}),e.isCustom&&(0,r.jsx)("span",{className:"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-500/20 text-blue-300",children:"Custom"})]}),e.isCustom&&e.databaseId&&(0,r.jsx)("button",{onClick:()=>to(e.databaseId,e.name),disabled:eZ===e.databaseId,className:"p-1.5 text-gray-400 hover:text-red-400 hover:bg-red-900/30 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2",title:"Delete this custom role",children:eZ===e.databaseId?(0,r.jsx)(m.A,{className:"h-4 w-4 animate-spin"}):(0,r.jsx)(v.A,{className:"h-4 w-4"})})]},e.id)})]})]}),(0,r.jsx)("div",{className:"p-6 border-t border-gray-800/50 bg-gray-900/50 rounded-b-lg",children:(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)("button",{onClick:()=>{ew(null),eK(!1),eW(null)},className:"btn-secondary",children:"Done"})})})]})})})(),eN&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,r.jsxs)("div",{className:"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-800/50",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Edit API Key"}),(0,r.jsx)("button",{onClick:()=>ek(null),className:"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200",children:(0,r.jsx)(j.A,{className:"h-6 w-6"})})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:eN.label}),(0,r.jsxs)("p",{className:"text-sm text-gray-400",children:["Current: ",eN.provider," (",eN.predefined_model_id,")"]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,r.jsx)("div",{className:"w-full p-2.5 bg-gray-800/50 border border-gray-700 rounded-md text-gray-300",children:(null==(t=l.MG.find(e=>e.id===eN.provider))?void 0:t.name)||eN.provider}),(0,r.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Provider cannot be changed"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"editModelId",className:"block text-sm font-medium text-gray-300 mb-2",children:"Model"}),(0,r.jsx)("select",{id:"editModelId",value:eA,onChange:e=>eI(e.target.value),disabled:!te.length,className:"w-full p-2.5 bg-gray-800/50 border border-gray-700 rounded-md text-gray-200 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-800/30",children:te.length>0?te.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value)):(0,r.jsx)("option",{value:"",disabled:!0,children:ed?"Loading models...":"No models available"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"editTemperature",className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature: ",e_]}),(0,r.jsx)("input",{type:"range",id:"editTemperature",min:"0",max:"2",step:"0.1",value:e_,onChange:e=>eC(parseFloat(e.target.value)),className:"slider-orange w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"}),(0,r.jsxs)("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[(0,r.jsx)("span",{children:"0.0 (Focused)"}),(0,r.jsx)("span",{children:"1.0 (Balanced)"}),(0,r.jsx)("span",{children:"2.0 (Creative)"})]})]}),(0,r.jsx)("div",{className:"bg-gray-800/50 rounded-lg p-3",children:(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied."})})]})]}),(0,r.jsx)("div",{className:"p-6 border-t border-gray-800/50 bg-gray-900/50 rounded-b-lg",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,r.jsx)("button",{onClick:()=>ek(null),className:"btn-secondary",disabled:eS,children:"Cancel"}),(0,r.jsx)("button",{onClick:tr,disabled:eS,className:"btn-primary",children:eS?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):"Save Changes"})]})})]})}),!L&&!z&&!er&&(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)(x.A,{className:"h-8 w-8 text-gray-400"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Model Not Found"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-8",children:"This API Model configuration could not be found or may have been deleted."}),(0,r.jsxs)("button",{onClick:()=>R("/my-models"),className:"inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Return to My API Models"]})]}),(0,r.jsx)(N.A,{isOpen:P.isOpen,onClose:P.hideConfirmation,onConfirm:P.onConfirm,title:P.title,message:P.message,confirmText:P.confirmText,cancelText:P.cancelText,type:P.type,isLoading:P.isLoading}),(0,r.jsx)(w.m_,{id:"global-tooltip"})]})})}},17974:(e,t,a)=>{"use strict";a.d(t,{BZ:()=>s.A,Gg:()=>i.A,OR:()=>l.A,Zu:()=>r.A});var r=a(78039),s=a(90345),i=a(62486),l=a(67508)},31547:(e,t,a)=>{"use strict";a.d(t,{QR:()=>s.A,Uz:()=>n.A,X_:()=>l.A,hc:()=>r.A,kU:()=>i.A});var r=a(1243),s=a(24357),i=a(92657),l=a(78749),n=a(69803)},38152:(e,t,a)=>{"use strict";a.d(t,{Pi:()=>r.A,fK:()=>i.A,uc:()=>s.A});var r=a(55628),s=a(31151),i=a(74500)},39499:(e,t,a)=>{"use strict";a.d(t,{Gg:()=>i.A,JD:()=>s.A,Kp:()=>r.A});var r=a(15713),s=a(15442),i=a(27305)},41448:(e,t,a)=>{"use strict";a.d(t,{B:()=>s.A,K:()=>r.A});var r=a(15713),s=a(86474)},44469:(e,t,a)=>{Promise.resolve().then(a.bind(a,14362))},47321:(e,t,a)=>{"use strict";a.d(t,{C1:()=>r.A,Pi:()=>s.A,qh:()=>i.A});var r=a(6865),s=a(55628),i=a(52589)},75898:(e,t,a)=>{"use strict";a.d(t,{FW:()=>s.A,Uz:()=>r.A,e9:()=>i.A});var r=a(69803),s=a(84616),i=a(53904)},75922:(e,t,a)=>{"use strict";a.d(t,{MG:()=>r});let r=[{id:"openai",name:"OpenAI",apiBaseUrl:"https://api.openai.com/v1/chat/completions",models:[]},{id:"google",name:"Google",apiBaseUrl:"https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",models:[]},{id:"anthropic",name:"Anthropic",apiBaseUrl:"https://api.anthropic.com/v1/chat/completions",models:[]},{id:"deepseek",name:"DeepSeek",apiBaseUrl:"https://api.deepseek.com/chat/completions",models:[]},{id:"xai",name:"xAI (Grok)",apiBaseUrl:"https://api.x.ai/v1/chat/completions",models:[]},{id:"openrouter",name:"OpenRouter",apiBaseUrl:"https://openrouter.ai/api/v1/chat/completions",models:[]}]},76288:(e,t,a)=>{"use strict";a.d(t,{X:()=>r.A});var r=a(54416)},87266:(e,t,a)=>{"use strict";a.d(t,{Il:()=>r.A,QR:()=>i.A,TB:()=>o.A,Vv:()=>s.A,ek:()=>n.A,qz:()=>l.A});var r=a(79397),s=a(69074),i=a(24357),l=a(34869),n=a(75525),o=a(62525)},99323:(e,t,a)=>{"use strict";a.d(t,{bu:()=>o,i9:()=>n});var r=a(95155),s=a(12115),i=a(35695);let l=(0,s.createContext)(void 0);function n(e){let{children:t}=e,[a,n]=(0,s.useState)(!1),[o,d]=(0,s.useState)(null),[c,u]=(0,s.useState)([]),[m,g]=(0,s.useState)(new Set),[x,p]=(0,s.useState)(!1),h=(0,i.usePathname)(),f=(0,i.useRouter)(),b=(0,s.useRef)(null),y=(0,s.useRef)([]),v=(0,s.useRef)(null),j=(0,s.useRef)(0),w=(0,s.useRef)({}),N=(0,s.useRef)({});(0,s.useEffect)(()=>{p(!0)},[]);let k=(0,s.useCallback)(e=>{},[x]);(0,s.useEffect)(()=>{h&&!c.includes(h)&&(u(e=>[...e,h]),g(e=>new Set([...e,h])))},[h,c]),(0,s.useEffect)(()=>{k("\uD83D\uDD0D [OPTIMISTIC NAV] Route check: target=".concat(o,", current=").concat(h,", navigationId=").concat(v.current)),o&&v.current&&h===o&&(k("✅ [OPTIMISTIC NAV] Navigation completed: ".concat(o," -> ").concat(h)),b.current&&(clearTimeout(b.current),b.current=null),n(!1),d(null),v.current=null,y.current=y.current.filter(e=>e.route!==o))},[h,o,k]),(0,s.useEffect)(()=>{a&&o&&h===o&&(k("\uD83D\uDE80 [OPTIMISTIC NAV] Immediate route match detected, clearing navigation state"),n(!1),d(null),b.current&&(clearTimeout(b.current),b.current=null))},[h,o,a,k]);let _=(0,s.useCallback)(e=>m.has(e),[m]),C=(0,s.useCallback)(()=>{if(0===y.current.length)return;let e=y.current[y.current.length-1];y.current=[e];let{route:t,id:a}=e;k("\uD83D\uDE80 [OPTIMISTIC NAV] Processing navigation to: ".concat(t," (id: ").concat(a,")")),b.current&&(clearTimeout(b.current),b.current=null),v.current=a;let r=_(t);r&&(k("⚡ [OPTIMISTIC NAV] Using cached navigation for: ".concat(t)),setTimeout(()=>{v.current===a&&n(!1)},100));try{f.push(t)}catch(e){k("❌ [OPTIMISTIC NAV] Router.push failed for: ".concat(t,", using fallback")),window.location.href=t;return}b.current=setTimeout(()=>{if(k("⚠️ [OPTIMISTIC NAV] Timeout reached for: ".concat(t," (id: ").concat(a,"), current path: ").concat(h)),v.current===a){k("\uD83D\uDD04 [OPTIMISTIC NAV] Attempting fallback navigation to: ".concat(t));try{window.location.href=t}catch(e){k("❌ [OPTIMISTIC NAV] Fallback navigation failed: ".concat(e))}n(!1),d(null),v.current=null}b.current=null},r?800:3e3)},[f,h,_,k]),A=(0,s.useCallback)(e=>{if(h===e||!x)return;let t=Date.now();if(t-j.current<100&&o===e)return void k("\uD83D\uDD04 [OPTIMISTIC NAV] Debouncing duplicate navigation to: ".concat(e));if(j.current=t,w.current[e]||(w.current[e]=0),w.current[e]++,N.current[e]&&clearTimeout(N.current[e]),N.current[e]=setTimeout(()=>{w.current[e]=0},2e3),w.current[e]>=3){k("\uD83D\uDEA8 [OPTIMISTIC NAV] Force navigation escape hatch for: ".concat(e)),w.current[e]=0,window.location.href=e;return}b.current&&(clearTimeout(b.current),b.current=null),n(!0),d(e);let a="nav_".concat(t,"_").concat(Math.random().toString(36).substr(2,9));y.current=[{route:e,timestamp:t,id:a}],C()},[h,o,C,k,x]),I=(0,s.useCallback)(()=>{b.current&&(clearTimeout(b.current),b.current=null),n(!1),d(null),v.current=null,y.current=[]},[]);return(0,s.useEffect)(()=>{if(!x)return;let e=()=>{!document.hidden&&a&&(k("\uD83D\uDC41️ [OPTIMISTIC NAV] Document visible, checking if navigation should clear"),setTimeout(()=>{o&&h===o&&(k("\uD83D\uDD27 [OPTIMISTIC NAV] Force clearing navigation state"),n(!1),d(null),b.current&&(clearTimeout(b.current),b.current=null))},100))};return document.addEventListener("visibilitychange",e),()=>document.removeEventListener("visibilitychange",e)},[a,o,h,k,x]),(0,s.useEffect)(()=>()=>{b.current&&clearTimeout(b.current)},[]),(0,r.jsx)(l.Provider,{value:{isNavigating:a,targetRoute:o,navigateOptimistically:A,clearNavigation:I,isPageCached:_,navigationHistory:c},children:t})}function o(){return(0,s.useContext)(l)||null}}},e=>{var t=t=>e(e.s=t);e.O(0,[8888,1459,7874,5738,9968,6060,6308,4755,563,2662,8669,4703,622,2432,408,1670,682,6642,7706,7544,2138,4518,9248,2324,7358],()=>t(44469)),_N_E=e.O()}]);