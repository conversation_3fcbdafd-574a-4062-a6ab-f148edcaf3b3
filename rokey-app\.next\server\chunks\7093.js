"use strict";exports.id=7093,exports.ids=[7093],exports.modules={6508:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(60687),a=r(43210),o=r(85814),i=r.n(o),l=r(16189);function n({href:e,children:t,className:r="",prefetch:o=!0}){let n=(0,l.useRouter)();return(0,s.jsx)(i(),{href:e,className:r,onClick:t=>{t.preventDefault(),(0,a.startTransition)(()=>{n.push(e)})},prefetch:o,children:t})}},13178:(e,t,r)=>{r.d(t,{j:()=>o});var s=r(43210),a=r(16189);function o(){let e=(0,a.useRouter)();return{navigateInstantly:(0,s.useCallback)(t=>{(0,s.startTransition)(()=>{e.push(t)})},[e])}}},57093:(e,t,r)=>{r.d(t,{A:()=>g});var s=r(60687),a=r(43210),o=r(85814),i=r.n(o),l=r(30474),n=r(52535),c=r(27010),d=r(81836),x=r(6510),h=r(6508),m=r(13178);function g(){let[e,t]=(0,a.useState)(!1),[r,o]=(0,a.useState)(!1),[g,u]=(0,a.useState)(!0),[b,p]=(0,a.useState)(null),[v,f]=(0,a.useState)(!0),[y,j]=(0,a.useState)(0),w=e=>{p(b===e?null:e)};return(0,m.j)(),(0,s.jsxs)(n.P.nav,{className:"fixed top-4 left-0 right-0 z-50 px-6",initial:{y:0,opacity:1},animate:{y:v?0:-100,opacity:+!!v},transition:{duration:.3,ease:[.25,.46,.45,.94]},children:[(0,s.jsxs)("div",{className:"flex items-center justify-between max-w-7xl mx-auto",children:[(0,s.jsx)("div",{className:"bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-4 py-1",children:(0,s.jsxs)(i(),{href:"/",className:"flex items-center space-x-3 group",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.default,{src:"/RouKey_Logo_NOGLOW.png",alt:"RouKey",width:44,height:44,className:"h-11 w-11 transition-opacity duration-200"}),(0,s.jsx)(l.default,{src:"/RouKey_Logo_GLOW.png",alt:"RouKey Glow",width:44,height:44,className:"h-11 w-11 absolute top-0 left-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200"})]}),(0,s.jsx)("span",{className:"text-lg font-bold text-white",children:"RouKey"})]})}),(0,s.jsx)("div",{className:"bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-6 py-3",children:(0,s.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{onClick:e=>{e.stopPropagation(),w("features")},className:"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium",children:[(0,s.jsx)("span",{children:"Features"}),(0,s.jsx)(c.A,{className:"w-3 h-3"})]}),"features"===b&&(0,s.jsxs)("div",{className:"absolute top-full left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50",children:[(0,s.jsx)(h.A,{href:"/features",className:"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm",children:"Product overview"}),(0,s.jsx)(h.A,{href:"/routing-strategies",className:"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm",children:"Routing"})]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{onClick:e=>{e.stopPropagation(),w("docs")},className:"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium",children:[(0,s.jsx)("span",{children:"Docs"}),(0,s.jsx)(c.A,{className:"w-3 h-3"})]}),"docs"===b&&(0,s.jsxs)("div",{className:"absolute top-full left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50",children:[(0,s.jsx)(h.A,{href:"/docs#overview",className:"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm",children:"Overview"}),(0,s.jsx)(h.A,{href:"/docs#features",className:"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm",children:"Features"}),(0,s.jsx)(h.A,{href:"/docs#api-reference",className:"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm",children:"API Reference"}),(0,s.jsx)(h.A,{href:"/docs#use-cases",className:"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm",children:"Use Cases"}),(0,s.jsx)(h.A,{href:"/docs#faq",className:"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm",children:"FAQ"})]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{onClick:e=>{e.stopPropagation(),w("about")},className:"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium",children:[(0,s.jsx)("span",{children:"About"}),(0,s.jsx)(c.A,{className:"w-3 h-3"})]}),"about"===b&&(0,s.jsxs)("div",{className:"absolute top-full left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50",children:[(0,s.jsx)(h.A,{href:"/about-developer",className:"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm",children:"About Developer"}),(0,s.jsx)(h.A,{href:"/about",className:"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm",children:"About RouKey"}),(0,s.jsx)(h.A,{href:"/contact",className:"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm",children:"Contact"})]})]}),(0,s.jsx)(h.A,{href:"/pricing",className:"text-gray-300 hover:text-white transition-colors text-sm font-medium",children:"Pricing"})]})}),(0,s.jsx)("div",{className:"bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-4 py-1",children:(0,s.jsx)("div",{className:"flex items-center space-x-3",children:!g&&(r?(0,s.jsx)(h.A,{href:"/dashboard",className:"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-6 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 font-semibold",children:"Dashboard"}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(h.A,{href:"/auth/signin",className:"text-gray-300 hover:text-white transition-colors text-sm font-medium",children:"Sign In"}),(0,s.jsxs)(h.A,{href:"/pricing",className:"relative inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 group overflow-hidden bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-400 hover:to-red-400",children:[(0,s.jsx)("span",{className:"relative z-10 font-semibold",children:"Get Started"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-transparent via-white/10 to-white/20 group-hover:via-white/20 group-hover:to-white/30 transition-all duration-200"})]})]}))})}),(0,s.jsx)("div",{className:"lg:hidden bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-4 py-2",children:(0,s.jsx)("button",{onClick:()=>t(!e),className:"text-gray-300 hover:text-white transition-colors",children:e?(0,s.jsx)(d.A,{className:"h-6 w-6"}):(0,s.jsx)(x.A,{className:"h-6 w-6"})})})]}),e&&(0,s.jsx)(n.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"lg:hidden mt-4 bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl mx-4 sm:mx-6 py-6",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-6 px-6",children:[(0,s.jsx)("div",{onClick:()=>t(!1),children:(0,s.jsx)(h.A,{href:"/features",className:"text-gray-300 hover:text-white transition-colors duration-100 py-2 text-lg font-medium block",children:"Features"})}),(0,s.jsx)("div",{onClick:()=>t(!1),children:(0,s.jsx)(h.A,{href:"/docs",className:"text-gray-300 hover:text-white transition-colors duration-100 py-2 text-lg font-medium block",children:"Docs"})}),(0,s.jsx)("div",{onClick:()=>t(!1),children:(0,s.jsx)(h.A,{href:"/about",className:"text-gray-300 hover:text-white transition-colors duration-100 py-2 text-lg font-medium block",children:"About"})}),(0,s.jsx)("div",{onClick:()=>t(!1),children:(0,s.jsx)(h.A,{href:"/pricing",className:"text-gray-300 hover:text-white transition-colors duration-100 py-2 text-lg font-medium block",children:"Pricing"})}),(0,s.jsx)("div",{className:"border-t border-gray-700 pt-6 mt-6",children:!g&&(r?(0,s.jsx)("div",{onClick:()=>t(!1),children:(0,s.jsx)(h.A,{href:"/dashboard",className:"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-6 py-3 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 font-semibold text-center block w-full",children:"Dashboard"})}):(0,s.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,s.jsx)("div",{onClick:()=>t(!1),children:(0,s.jsx)(h.A,{href:"/auth/signin",className:"text-gray-300 hover:text-white transition-colors text-center py-2 text-lg font-medium block",children:"Sign In"})}),(0,s.jsx)("div",{onClick:()=>t(!1),children:(0,s.jsx)(h.A,{href:"/pricing",className:"bg-gradient-to-r from-amber-500 to-orange-500 text-white px-6 py-3 rounded-lg text-center font-medium hover:shadow-lg transition-all duration-200 block w-full",children:"Get Started"})})]}))})]})})]})}}};